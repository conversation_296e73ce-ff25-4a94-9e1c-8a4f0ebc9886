defmodule Repobot.RepositorySourceFile do
  use Ecto.Schema
  import Ecto.Changeset
  import Ecto.Query

  alias <PERSON>obot.{Repo, SourceFile}

  @primary_key {:id, :binary_id, autogenerate: true}
  @foreign_key_type :binary_id

  schema "repository_source_files" do
    belongs_to :repository, Repobot.Repository, type: :binary_id
    belongs_to :source_file, Repobot.SourceFile, type: :binary_id

    timestamps(type: :utc_datetime)
  end

  @doc false
  def changeset(repository_source_file, attrs) do
    repository_source_file
    |> cast(attrs, [:repository_id, :source_file_id])
    |> validate_required([:repository_id, :source_file_id])
    |> validate_unique_target_path()
    |> unique_constraint([:repository_id, :source_file_id])
  end

  # Validates that no other source file with the same target_path is already
  # associated with the same repository.
  defp validate_unique_target_path(changeset) do
    repository_id = get_field(changeset, :repository_id)
    source_file_id = get_field(changeset, :source_file_id)
    current_id = get_field(changeset, :id)

    if repository_id && source_file_id do
      # Get the target_path of the source file being associated
      case Repo.get(SourceFile, source_file_id) do
        %SourceFile{target_path: target_path} ->
          # Check if any other source file with the same target_path is already associated
          # This prevents different source files with the same target path from being
          # associated with the same repository, but allows the same source file to be
          # associated with multiple repositories
          query =
            from(rsf in __MODULE__,
              join: sf in SourceFile,
              on: rsf.source_file_id == sf.id,
              where:
                rsf.repository_id == ^repository_id and
                  sf.target_path == ^target_path and
                  rsf.source_file_id != ^source_file_id,
              select: sf.target_path
            )

          # Only exclude current record if it has an ID (for updates)
          query =
            if current_id do
              from(rsf in query, where: rsf.id != ^current_id)
            else
              query
            end

          existing_association = Repo.one(query)

          if existing_association do
            add_error(
              changeset,
              :source_file_id,
              "A source file with target path \"#{target_path}\" already exists in this repository"
            )
          else
            changeset
          end

        nil ->
          add_error(changeset, :source_file_id, "Source file not found")
      end
    else
      changeset
    end
  end
end
