defmodule Repobot.Repo.Migrations.AddUniqueTargetPathPerRepositoryConstraint do
  use Ecto.Migration

  def up do
    # Since we can't create a direct unique index across tables, we'll use a different approach
    # We'll add a database function and trigger to enforce the constraint

    # Create a function to check for duplicate target paths
    execute """
    CREATE OR REPLACE FUNCTION check_unique_target_path_per_repository()
    RETURNS TRIGGER AS $$
    BEGIN
      IF EXISTS (
        SELECT 1
        FROM repository_source_files rsf
        JOIN source_files sf ON rsf.source_file_id = sf.id
        WHERE rsf.repository_id = NEW.repository_id
        AND sf.target_path = (SELECT target_path FROM source_files WHERE id = NEW.source_file_id)
        AND rsf.id != NEW.id
      ) THEN
        RAISE EXCEPTION 'A source file with target path "%" already exists in this repository',
          (SELECT target_path FROM source_files WHERE id = NEW.source_file_id);
      END IF;
      RETURN NEW;
    END;
    $$ LANGUAGE plpgsql;
    """

    # Create a trigger that runs the validation function before insert and update
    execute """
    CREATE TRIGGER ensure_unique_target_path_per_repository
    BEFORE INSERT OR UPDATE ON repository_source_files
    FOR EACH ROW
    EXECUTE FUNCTION check_unique_target_path_per_repository();
    """
  end

  def down do
    execute "DROP TRIGGER IF EXISTS ensure_unique_target_path_per_repository ON repository_source_files"
    execute "DROP FUNCTION IF EXISTS check_unique_target_path_per_repository()"
  end
end
