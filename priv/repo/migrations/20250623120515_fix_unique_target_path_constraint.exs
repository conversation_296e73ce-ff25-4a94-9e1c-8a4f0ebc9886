defmodule Repobot.Repo.Migrations.FixUniqueTargetPathConstraint do
  use Ecto.Migration

  def up do
    # Update the function to exclude the current source file being associated
    # This allows the same source file to be associated with multiple repositories
    execute """
    CREATE OR REPLACE FUNCTION check_unique_target_path_per_repository()
    RETURNS TRIGGER AS $$
    BEGIN
      IF EXISTS (
        SELECT 1
        FROM repository_source_files rsf
        JOIN source_files sf ON rsf.source_file_id = sf.id
        WHERE rsf.repository_id = NEW.repository_id
        AND sf.target_path = (SELECT target_path FROM source_files WHERE id = NEW.source_file_id)
        AND rsf.source_file_id != NEW.source_file_id
        AND rsf.id != NEW.id
      ) THEN
        RAISE EXCEPTION 'A source file with target path "%" already exists in this repository',
          (SELECT target_path FROM source_files WHERE id = NEW.source_file_id);
      END IF;
      RETURN NEW;
    END;
    $$ LANGUAGE plpgsql;
    """
  end

  def down do
    # Revert to the original function
    execute """
    CREATE OR REPLACE FUNCTION check_unique_target_path_per_repository()
    RETURNS TRIGGER AS $$
    BEGIN
      IF EXISTS (
        SELECT 1
        FROM repository_source_files rsf
        JOIN source_files sf ON rsf.source_file_id = sf.id
        WHERE rsf.repository_id = NEW.repository_id
        AND sf.target_path = (SELECT target_path FROM source_files WHERE id = NEW.source_file_id)
        AND rsf.id != NEW.id
      ) THEN
        RAISE EXCEPTION 'A source file with target path "%" already exists in this repository',
          (SELECT target_path FROM source_files WHERE id = NEW.source_file_id);
      END IF;
      RETURN NEW;
    END;
    $$ LANGUAGE plpgsql;
    """
  end
end
